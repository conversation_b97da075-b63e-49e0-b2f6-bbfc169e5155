
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1920, height=1080, initial-scale=1.0">
    <title>AI+Python提高工作效率 - 动态波浪版</title>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        /* =================================================================== */
        /* === 1. 全局设计变量 === */
        /* =================================================================== */
        :root {
            /* Updated colors for light wave background */
            --text-color: #1d1d1f;
            --heading-color: #1d1d1f;
            --accent-color: #007AFF; /* Apple blue accent */
            --subtle-text-color: rgba(29, 29, 31, 0.8);

            /* Card variables adapted for light background with enhanced glass effect */
            --card-bg-color: rgba(255, 255, 255, 0.25);
            --card-border-color: rgba(255, 255, 255, 0.4);
            --card-blur: 25px;
            --card-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            color: var(--text-color);

            /* 使用提供的图片作为背景 */
            background: url('未命名.001.jpeg') center center / cover no-repeat;
            position: relative;
        }

        /* 移除波浪背景，使用图片背景 */

        .wave1 {
            background: linear-gradient(45deg,
                rgba(0, 122, 255, 0.05) 0%,
                rgba(0, 122, 255, 0.12) 25%,
                rgba(0, 122, 255, 0.08) 50%,
                rgba(0, 122, 255, 0.04) 75%,
                transparent 100%);
            animation: dynamicWave1 18s ease-in-out infinite;
            transform-origin: 20% 80%;
            left: -10%;
            top: -10%;
        }

        .wave2 {
            background: radial-gradient(ellipse 150% 100% at 70% 30%,
                transparent 0%,
                rgba(88, 86, 214, 0.06) 20%,
                rgba(88, 86, 214, 0.12) 40%,
                rgba(88, 86, 214, 0.08) 60%,
                rgba(88, 86, 214, 0.04) 80%,
                transparent 100%);
            animation: dynamicWave2 22s ease-in-out infinite;
            transform-origin: 70% 30%;
            left: -5%;
            top: -5%;
        }

        .wave3 {
            background: conic-gradient(from 45deg at 40% 60%,
                rgba(175, 82, 222, 0.08) 0deg,
                transparent 60deg,
                rgba(175, 82, 222, 0.12) 120deg,
                transparent 180deg,
                rgba(175, 82, 222, 0.06) 240deg,
                transparent 300deg,
                rgba(175, 82, 222, 0.08) 360deg);
            animation: dynamicWave3 26s ease-in-out infinite;
            transform-origin: 40% 60%;
            left: -8%;
            top: -8%;
        }

        .wave4 {
            background: radial-gradient(ellipse 120% 80% at 80% 20%,
                rgba(255, 45, 146, 0.06) 0%,
                rgba(255, 45, 146, 0.10) 25%,
                rgba(255, 45, 146, 0.08) 50%,
                rgba(255, 45, 146, 0.04) 75%,
                transparent 100%);
            animation: dynamicWave4 20s ease-in-out infinite;
            transform-origin: 80% 20%;
            left: -6%;
            top: -6%;
        }

        .wave5 {
            background: linear-gradient(135deg,
                transparent 0%,
                rgba(52, 199, 89, 0.05) 20%,
                rgba(52, 199, 89, 0.10) 40%,
                rgba(52, 199, 89, 0.08) 60%,
                rgba(52, 199, 89, 0.04) 80%,
                transparent 100%);
            animation: dynamicWave5 24s ease-in-out infinite;
            transform-origin: 60% 40%;
            left: -4%;
            top: -4%;
        }

        .wave6 {
            background: radial-gradient(circle at 90% 90%,
                rgba(255, 159, 10, 0.06) 0%,
                rgba(255, 159, 10, 0.10) 30%,
                rgba(255, 159, 10, 0.06) 60%,
                transparent 100%);
            animation: dynamicWave6 16s ease-in-out infinite;
            transform-origin: 90% 90%;
            left: -7%;
            top: -7%;
        }

        /* Floating particles for enhanced visual effect */
        .particle {
            position: absolute;
            border-radius: 50%;
            pointer-events: none;
            animation: float infinite ease-in-out;
        }

        .particle1 {
            width: 12px;
            height: 12px;
            background: rgba(0, 122, 255, 0.6);
            top: 20%;
            left: 10%;
            animation: float1 8s infinite ease-in-out;
            box-shadow: 0 0 20px rgba(0, 122, 255, 0.4);
        }

        .particle2 {
            width: 16px;
            height: 16px;
            background: rgba(88, 86, 214, 0.5);
            top: 60%;
            left: 80%;
            animation: float2 12s infinite ease-in-out;
            box-shadow: 0 0 25px rgba(88, 86, 214, 0.3);
        }

        .particle3 {
            width: 10px;
            height: 10px;
            background: rgba(175, 82, 222, 0.7);
            top: 80%;
            left: 30%;
            animation: float3 10s infinite ease-in-out;
            box-shadow: 0 0 18px rgba(175, 82, 222, 0.4);
        }

        .particle4 {
            width: 14px;
            height: 14px;
            background: rgba(255, 45, 146, 0.6);
            top: 30%;
            left: 70%;
            animation: float4 14s infinite ease-in-out;
            box-shadow: 0 0 22px rgba(255, 45, 146, 0.3);
        }

        .particle5 {
            width: 11px;
            height: 11px;
            background: rgba(52, 199, 89, 0.65);
            top: 70%;
            left: 15%;
            animation: float5 9s infinite ease-in-out;
            box-shadow: 0 0 20px rgba(52, 199, 89, 0.4);
        }

        .particle6 {
            width: 8px;
            height: 8px;
            background: rgba(255, 159, 10, 0.6);
            top: 15%;
            left: 50%;
            animation: float6 11s infinite ease-in-out;
            box-shadow: 0 0 16px rgba(255, 159, 10, 0.4);
        }

        .particle7 {
            width: 13px;
            height: 13px;
            background: rgba(255, 204, 0, 0.5);
            top: 45%;
            left: 25%;
            animation: float7 13s infinite ease-in-out;
            box-shadow: 0 0 24px rgba(255, 204, 0, 0.3);
        }

        .particle8 {
            width: 9px;
            height: 9px;
            background: rgba(255, 59, 48, 0.6);
            top: 85%;
            left: 75%;
            animation: float8 7s infinite ease-in-out;
            box-shadow: 0 0 18px rgba(255, 59, 48, 0.4);
        }

        .particle9 {
            width: 15px;
            height: 15px;
            background: rgba(90, 200, 250, 0.5);
            top: 35%;
            left: 5%;
            animation: float9 15s infinite ease-in-out;
            box-shadow: 0 0 28px rgba(90, 200, 250, 0.3);
        }

        .particle10 {
            width: 7px;
            height: 7px;
            background: rgba(162, 132, 94, 0.6);
            top: 55%;
            left: 90%;
            animation: float10 9s infinite ease-in-out;
            box-shadow: 0 0 14px rgba(162, 132, 94, 0.4);
        }

        .particle11 {
            width: 12px;
            height: 12px;
            background: rgba(120, 220, 120, 0.6);
            top: 10%;
            left: 85%;
            animation: float11 10s infinite ease-in-out;
            box-shadow: 0 0 20px rgba(120, 220, 120, 0.4);
        }

        .particle12 {
            width: 18px;
            height: 18px;
            background: rgba(200, 100, 255, 0.4);
            top: 75%;
            left: 55%;
            animation: float12 16s infinite ease-in-out;
            box-shadow: 0 0 30px rgba(200, 100, 255, 0.3);
        }

        /* Additional wave layers for more dynamic effect */
        .wave7 {
            background: linear-gradient(60deg,
                rgba(255, 204, 0, 0.04) 0%,
                rgba(255, 204, 0, 0.08) 30%,
                rgba(255, 204, 0, 0.06) 60%,
                transparent 100%);
            animation: dynamicWave7 19s ease-in-out infinite;
            transform-origin: 25% 75%;
            left: -3%;
            top: -3%;
        }

        .wave8 {
            background: radial-gradient(ellipse 200% 150% at 10% 50%,
                rgba(255, 59, 48, 0.05) 0%,
                rgba(255, 59, 48, 0.09) 40%,
                rgba(255, 59, 48, 0.04) 80%,
                transparent 100%);
            animation: dynamicWave8 23s ease-in-out infinite;
            transform-origin: 10% 50%;
            left: -9%;
            top: -9%;
        }

        /* Enhanced dynamic wave animations with fluid motion */
        @keyframes dynamicWave1 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.7;
            }
            25% {
                transform: rotate(90deg) scale(1.15) translate(30px, -20px);
                opacity: 0.9;
            }
            50% {
                transform: rotate(180deg) scale(0.9) translate(-15px, 25px);
                opacity: 0.8;
            }
            75% {
                transform: rotate(270deg) scale(1.08) translate(20px, -10px);
                opacity: 0.95;
            }
            100% {
                transform: rotate(360deg) scale(1) translate(0, 0);
                opacity: 0.7;
            }
        }

        @keyframes dynamicWave2 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.6;
            }
            30% {
                transform: rotate(-120deg) scale(1.2) translate(-25px, 15px);
                opacity: 0.85;
            }
            60% {
                transform: rotate(-240deg) scale(0.85) translate(35px, -30px);
                opacity: 0.75;
            }
            100% {
                transform: rotate(-360deg) scale(1) translate(0, 0);
                opacity: 0.6;
            }
        }

        @keyframes dynamicWave3 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.5;
            }
            20% {
                transform: rotate(72deg) scale(1.25) translate(40px, 20px);
                opacity: 0.8;
            }
            40% {
                transform: rotate(144deg) scale(0.8) translate(-30px, 35px);
                opacity: 0.9;
            }
            60% {
                transform: rotate(216deg) scale(1.1) translate(25px, -25px);
                opacity: 0.7;
            }
            80% {
                transform: rotate(288deg) scale(0.95) translate(-20px, 15px);
                opacity: 0.85;
            }
            100% {
                transform: rotate(360deg) scale(1) translate(0, 0);
                opacity: 0.5;
            }
        }

        @keyframes dynamicWave4 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.8;
            }
            33% {
                transform: rotate(120deg) scale(1.18) translate(-40px, 30px);
                opacity: 0.95;
            }
            66% {
                transform: rotate(240deg) scale(0.88) translate(35px, -20px);
                opacity: 0.75;
            }
            100% {
                transform: rotate(360deg) scale(1) translate(0, 0);
                opacity: 0.8;
            }
        }

        @keyframes dynamicWave5 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.65;
            }
            40% {
                transform: rotate(-144deg) scale(1.12) translate(50px, -35px);
                opacity: 0.9;
            }
            80% {
                transform: rotate(-288deg) scale(0.92) translate(-25px, 40px);
                opacity: 0.8;
            }
            100% {
                transform: rotate(-360deg) scale(1) translate(0, 0);
                opacity: 0.65;
            }
        }

        @keyframes dynamicWave6 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.7;
            }
            50% {
                transform: rotate(180deg) scale(1.3) translate(-45px, 25px);
                opacity: 0.95;
            }
            100% {
                transform: rotate(360deg) scale(1) translate(0, 0);
                opacity: 0.7;
            }
        }

        /* Floating particle animations */
        @keyframes float1 {
            0%, 100% {
                transform: translateY(0px) translateX(0px);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-20px) translateX(15px);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-40px) translateX(-10px);
                opacity: 0.8;
            }
            75% {
                transform: translateY(-20px) translateX(20px);
                opacity: 0.5;
            }
        }

        @keyframes float2 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.25;
            }
            33% {
                transform: translateY(-30px) translateX(-25px) scale(1.2);
                opacity: 0.5;
            }
            66% {
                transform: translateY(-15px) translateX(30px) scale(0.8);
                opacity: 0.7;
            }
        }

        @keyframes float3 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg);
                opacity: 0.4;
            }
            50% {
                transform: translateY(-35px) translateX(25px) rotate(180deg);
                opacity: 0.8;
            }
        }

        @keyframes float4 {
            0%, 100% {
                transform: translateY(0px) translateX(0px);
                opacity: 0.3;
            }
            20% {
                transform: translateY(-15px) translateX(-20px);
                opacity: 0.6;
            }
            40% {
                transform: translateY(-30px) translateX(10px);
                opacity: 0.8;
            }
            60% {
                transform: translateY(-25px) translateX(-15px);
                opacity: 0.7;
            }
            80% {
                transform: translateY(-10px) translateX(25px);
                opacity: 0.5;
            }
        }

        @keyframes float5 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.35;
            }
            40% {
                transform: translateY(-25px) translateX(20px) scale(1.3);
                opacity: 0.7;
            }
            80% {
                transform: translateY(-45px) translateX(-15px) scale(0.9);
                opacity: 0.6;
            }
        }

        @keyframes dynamicWave7 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.6;
            }
            35% {
                transform: rotate(126deg) scale(1.15) translate(35px, -40px);
                opacity: 0.85;
            }
            70% {
                transform: rotate(252deg) scale(0.9) translate(-30px, 30px);
                opacity: 0.75;
            }
            100% {
                transform: rotate(360deg) scale(1) translate(0, 0);
                opacity: 0.6;
            }
        }

        @keyframes dynamicWave8 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.55;
            }
            25% {
                transform: rotate(-90deg) scale(1.25) translate(-50px, 20px);
                opacity: 0.8;
            }
            50% {
                transform: rotate(-180deg) scale(0.85) translate(40px, -35px);
                opacity: 0.9;
            }
            75% {
                transform: rotate(-270deg) scale(1.1) translate(-25px, 45px);
                opacity: 0.7;
            }
            100% {
                transform: rotate(-360deg) scale(1) translate(0, 0);
                opacity: 0.55;
            }
        }

        /* 新增粒子动画 */
        @keyframes float6 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-40px) translateX(30px) rotate(180deg) scale(1.4);
                opacity: 0.9;
            }
        }

        @keyframes float7 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.5;
            }
            33% {
                transform: translateY(-30px) translateX(-20px) scale(1.3);
                opacity: 0.8;
            }
            66% {
                transform: translateY(-50px) translateX(40px) scale(0.9);
                opacity: 0.7;
            }
        }

        @keyframes float8 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg);
                opacity: 0.6;
            }
            25% {
                transform: translateY(-20px) translateX(25px) rotate(90deg);
                opacity: 0.8;
            }
            50% {
                transform: translateY(-35px) translateX(-15px) rotate(180deg);
                opacity: 0.9;
            }
            75% {
                transform: translateY(-15px) translateX(35px) rotate(270deg);
                opacity: 0.7;
            }
        }

        @keyframes float9 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.5;
            }
            50% {
                transform: translateY(-60px) translateX(-40px) scale(1.5);
                opacity: 0.9;
            }
        }

        @keyframes float10 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
                opacity: 0.6;
            }
            40% {
                transform: translateY(-25px) translateX(-30px) rotate(144deg) scale(1.2);
                opacity: 0.8;
            }
            80% {
                transform: translateY(-45px) translateX(20px) rotate(288deg) scale(0.8);
                opacity: 0.7;
            }
        }

        @keyframes float11 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.6;
            }
            30% {
                transform: translateY(-35px) translateX(25px) scale(1.3);
                opacity: 0.9;
            }
            70% {
                transform: translateY(-20px) translateX(-35px) scale(0.9);
                opacity: 0.8;
            }
        }

        @keyframes float12 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
                opacity: 0.4;
            }
            25% {
                transform: translateY(-30px) translateX(-45px) rotate(90deg) scale(1.4);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-55px) translateX(20px) rotate(180deg) scale(1.1);
                opacity: 0.9;
            }
            75% {
                transform: translateY(-25px) translateX(50px) rotate(270deg) scale(1.2);
                opacity: 0.6;
            }
        }
        
        /* Main layout and transitions remain the same */
        .main {
            width: 100%;
            height: 100%;
            display: flex;
            transition: transform 1s cubic-bezier(0.86, 0, 0.07, 1);
        }

        .page {
            width: 100vw;
            height: 100vh;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 5vw;
        }
        
        .page-inner {
            width: 100%;
            max-width: 1200px;
            text-align: center;
            opacity: 0;
            transform: translateY(30px);
            transition: none;
        }
        .page.active .page-inner {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.8s ease-out 0.4s, transform 0.8s ease-out 0.4s;
        }
        
        /* =================================================================== */
        /* === 2. Card Design (已根据参考文件更新) === */
        /* =================================================================== */

        /* 增强的毛玻璃卡片效果 */
        .card, .grid-item {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(30px) saturate(200%);
            -webkit-backdrop-filter: blur(30px) saturate(200%);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                0 12px 40px 0 rgba(0, 0, 0, 0.2),
                0 4px 20px 0 rgba(0, 0, 0, 0.15),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.5),
                inset 0 -1px 0 0 rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .card::before, .grid-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.1) 100%);
            pointer-events: none;
            z-index: 1;
        }

        .card {
            display: flex;
            align-items: center;
            text-align: left;
            padding: 1.5rem 2rem;
            position: relative;
            z-index: 2;
        }

        /* 增强的悬停效果 */
        .card:hover, .grid-item:hover {
            transform: translateY(-12px) scale(1.02);
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(35px) saturate(220%);
            -webkit-backdrop-filter: blur(35px) saturate(220%);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow:
                0 20px 60px 0 rgba(0, 0, 0, 0.25),
                0 8px 30px 0 rgba(0, 0, 0, 0.18),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.6),
                inset 0 -1px 0 0 rgba(255, 255, 255, 0.3);
        }

        /* All other styles remain largely the same, but adapted for the new theme */
        .content-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; width: 100%; }
        .grid-item { padding: 2rem; }
        
        h1 { font-size: 4rem; font-weight: 600; color: var(--heading-color); margin-bottom: 1rem; }
        h2 { font-size: 3rem; font-weight: 500; color: var(--heading-color); margin-bottom: 3rem; display: inline-block; border-bottom: 2px solid var(--accent-color); padding-bottom: 1rem; }
        h3 { font-size: 1.6rem; font-weight: 500; color: var(--heading-color); margin-bottom: 1rem; }
        p { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .subtitle { font-size: 1.6rem; color: var(--subtle-text-color); font-weight: 300; }
        
        .card-list { list-style: none; display: flex; flex-direction: column; gap: 1.5rem; width: 100%; max-width: 900px; margin: 0 auto; }
        .card-content { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .card i.fas { color: var(--accent-color); font-size: 1.8rem; margin-right: 1.5rem; width: 35px; text-align: center; }
        .highlight { color: var(--accent-color); font-weight: 500; }
        
        .grid-item i.fas { font-size: 2.5rem; margin: 0 0 1rem 0; display: block; width: auto; color: var(--accent-color); }
        
        .warning-box { background-color: var(--card-bg-color); color: var(--text-color); backdrop-filter: blur(var(--card-blur)); border: 1px solid var(--accent-color); padding: 0.8rem 1.5rem; border-radius: 8px; font-weight: 700; display: inline-flex; align-items: center; margin-top: 1rem; }
        .warning-box i { margin-right: 10px; color: var(--accent-color); }

        /* 弹出卡片样式 */
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .popup-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .popup-card {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(40px) saturate(200%);
            -webkit-backdrop-filter: blur(40px) saturate(200%);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow:
                0 25px 80px 0 rgba(0, 0, 0, 0.3),
                0 10px 40px 0 rgba(0, 0, 0, 0.2),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.6),
                inset 0 -1px 0 0 rgba(255, 255, 255, 0.3);
            max-width: 800px;
            max-height: 80vh;
            width: 90%;
            padding: 3rem;
            position: relative;
            transform: scale(0.8) translateY(50px);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            overflow-y: auto;
        }

        .popup-overlay.active .popup-card {
            transform: scale(1) translateY(0);
        }

        .popup-close {
            position: absolute;
            top: 20px;
            right: 25px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 18px;
            color: var(--text-color);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .popup-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .popup-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: var(--heading-color);
            margin-bottom: 2rem;
            text-align: center;
            background: linear-gradient(135deg, var(--accent-color), #5856d6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .popup-content {
            font-size: 1.3rem;
            line-height: 1.8;
            color: var(--text-color);
        }

        .popup-section {
            margin-bottom: 2rem;
        }

        .popup-section h3 {
            font-size: 1.6rem;
            font-weight: 500;
            color: var(--accent-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .popup-section h3 i {
            margin-right: 0.8rem;
            font-size: 1.4rem;
        }

        .popup-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }

        .popup-feature {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .popup-feature:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-5px);
        }

        .popup-feature h4 {
            font-size: 1.3rem;
            font-weight: 500;
            color: var(--accent-color);
            margin-bottom: 0.8rem;
            display: flex;
            align-items: center;
        }

        .popup-feature h4 i {
            margin-right: 0.8rem;
            font-size: 1.2rem;
        }

        .clickable-text {
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .clickable-text:hover {
            color: var(--accent-color);
            text-shadow: 0 0 10px rgba(0, 122, 255, 0.3);
        }

        .clickable-text::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--accent-color);
            transition: width 0.3s ease;
        }

        .clickable-text:hover::after {
            width: 100%;
        }

        /* Navigation elements remain the same */
        .pagination { position: fixed; bottom: 30px; left: 50%; transform: translateX(-50%); list-style: none; z-index: 1000; display: flex; gap: 15px; padding: 8px 15px; background: rgba(0, 0, 0, 0.2); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; }
        .pagination-item { width: 12px; height: 12px; border-radius: 50%; background-color: rgba(255, 255, 255, 0.4); cursor: pointer; transition: background-color 0.3s, transform 0.3s; }
        .pagination-item.active { background-color: var(--accent-color); transform: scale(1.4); }
        .nav-button { position: fixed; top: 50%; transform: translateY(-50%); background-color: rgba(255, 255, 255, 0.1); color: white; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 50%; width: 50px; height: 50px; font-size: 20px; cursor: pointer; z-index: 100; display: flex; justify-content: center; align-items: center; transition: background-color 0.3s, opacity 0.3s; opacity: 0.7; backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); }
        .nav-button:hover { background-color: rgba(255, 255, 255, 0.2); opacity: 1; }
        .nav-button.hidden { opacity: 0; pointer-events: none; }
        #prev-btn { left: 30px; }
        #next-btn { right: 30px; }

        /* Main layout and transitions */
        .main {
            width: 100%;
            height: 100%;
            display: flex;
            transition: transform 1s cubic-bezier(0.86, 0, 0.07, 1);
        }

        .page {
            width: 100vw;
            height: 100vh;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 5vw;
        }

        .page-inner {
            width: 100%;
            max-width: 1200px;
            text-align: center;
            opacity: 0;
            transform: translateY(30px);
            transition: none;
        }
        .page.active .page-inner {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.8s ease-out 0.4s, transform 0.8s ease-out 0.4s;
        }

        /* Enhanced glass morphism card style */
        .card, .grid-item {
            background: var(--card-bg-color);
            backdrop-filter: blur(var(--card-blur)) saturate(180%);
            -webkit-backdrop-filter: blur(var(--card-blur)) saturate(180%);
            border-radius: var(--card-radius);
            border: 1px solid var(--card-border-color);
            box-shadow:
                0 8px 32px 0 rgba(0, 0, 0, 0.1),
                0 2px 16px 0 rgba(0, 0, 0, 0.08),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.4);
            transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
        }
        .card { display: flex; align-items: center; text-align: left; padding: 1.5rem 2rem; }

        /* Enhanced hover effect for glass morphism */
        .card:hover, .grid-item:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.35);
            box-shadow:
                0 16px 48px 0 rgba(0, 0, 0, 0.15),
                0 4px 24px 0 rgba(0, 0, 0, 0.12),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.6);
        }

        .content-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; width: 100%; }
        .grid-item { padding: 2rem; }

        h1 { font-size: 4rem; font-weight: 600; color: var(--heading-color); margin-bottom: 1rem; }
        h2 { font-size: 3rem; font-weight: 500; color: var(--heading-color); margin-bottom: 3rem; display: inline-block; border-bottom: 2px solid var(--accent-color); padding-bottom: 1rem; }
        h3 { font-size: 1.6rem; font-weight: 500; color: var(--heading-color); margin-bottom: 1rem; }
        p { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .subtitle { font-size: 1.6rem; color: var(--subtle-text-color); font-weight: 300; }

        .card-list { list-style: none; display: flex; flex-direction: column; gap: 1.5rem; width: 100%; max-width: 900px; margin: 0 auto; }
        .card-content { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .card i.fas { color: var(--accent-color); font-size: 1.8rem; margin-right: 1.5rem; width: 35px; text-align: center; }
        .highlight { color: var(--accent-color); font-weight: 500; }

        .grid-item i.fas { font-size: 2.5rem; margin: 0 0 1rem 0; display: block; width: auto; color: var(--accent-color); }

        .warning-box { background-color: var(--card-bg-color); color: var(--text-color); backdrop-filter: blur(var(--card-blur)); border: 1px solid var(--accent-color); padding: 0.8rem 1.5rem; border-radius: 8px; font-weight: 700; display: inline-flex; align-items: center; margin-top: 1rem; }
        .warning-box i { margin-right: 10px; color: var(--accent-color); }

        /* 可点击文本样式 */
        .clickable-text {
            cursor: pointer;
            color: var(--accent-color);
            text-decoration: underline;
            transition: all 0.3s ease;
        }
        .clickable-text:hover {
            color: #0056b3;
            text-shadow: 0 0 8px rgba(0, 122, 255, 0.3);
        }

        /* Python应用弹出卡片样式 */
        .python-popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            z-index: 10000;
            display: none;
            justify-content: center;
            align-items: center;
            animation: fadeIn 0.3s ease;
        }

        .python-popup {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(40px) saturate(200%);
            -webkit-backdrop-filter: blur(40px) saturate(200%);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow:
                0 20px 60px 0 rgba(0, 0, 0, 0.3),
                0 8px 30px 0 rgba(0, 0, 0, 0.2),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.6);
            padding: 2.5rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            animation: slideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .python-popup-close {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            color: var(--text-color);
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        .python-popup-close:hover {
            opacity: 1;
        }

        .python-popup h3 {
            color: var(--accent-color);
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .python-apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .python-app-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            text-align: center;
        }

        .python-app-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .python-app-card .app-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            display: block;
        }

        .python-app-card h4 {
            color: var(--heading-color);
            margin-bottom: 0.8rem;
            font-size: 1.3rem;
        }

        .python-app-card p {
            font-size: 1rem;
            line-height: 1.5;
            color: var(--subtle-text-color);
        }

        .pixel-character {
            display: inline-block;
            font-size: 1.5rem;
            animation: bounce 2s infinite;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(50px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        /* 警告弹出卡片样式 */
        .warning-popup {
            position: fixed;
            background: rgba(255, 59, 48, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                0 8px 25px rgba(255, 59, 48, 0.4),
                0 4px 15px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            font-size: 0.9rem;
            font-weight: 600;
            z-index: 1000;
            opacity: 0;
            transform: scale(0.8) translateY(10px);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            pointer-events: none;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .warning-popup.show {
            opacity: 1;
            transform: scale(1) translateY(0);
            pointer-events: auto;
        }

        .warning-popup::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 30px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid rgba(255, 59, 48, 0.95);
        }

        .warning-popup i {
            font-size: 1rem;
            animation: pulse 2s infinite;
        }

        .clickable-card {
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
        }

        .clickable-card:hover {
            transform: translateY(-8px) scale(1.02);
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }
    </style>
</head>
<body>
    <!-- Enhanced dynamic wave background container -->
    <div class="wave-container">
        <div class="wave wave1"></div>
        <div class="wave wave2"></div>
        <div class="wave wave3"></div>
        <div class="wave wave4"></div>
        <div class="wave wave5"></div>
        <div class="wave wave6"></div>
        <div class="wave wave7"></div>
        <div class="wave wave8"></div>

        <!-- 增强的浮动粒子效果 -->
        <div class="particle particle1"></div>
        <div class="particle particle2"></div>
        <div class="particle particle3"></div>
        <div class="particle particle4"></div>
        <div class="particle particle5"></div>
        <div class="particle particle6"></div>
        <div class="particle particle7"></div>
        <div class="particle particle8"></div>
        <div class="particle particle9"></div>
        <div class="particle particle10"></div>
        <div class="particle particle11"></div>
        <div class="particle particle12"></div>
    </div>

    <main class="main">
        <!-- The HTML content of all 9 pages remains exactly the same -->
        <!-- Slide 1: 封面页 -->
        <div class="page">
            <div class="page-inner">
                <h1>利用AI+Python实现工作效能新飞跃</h1>
                <p class="subtitle">拥抱变革</p>
                <p class="subtitle">演讲人：林东</p>
            </div>
        </div>
        <!-- Slide 2: 挑战与机遇 -->
        <div class="page">
            <div class="page-inner">
                <h2>挑战与机遇</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-cogs"></i><div class="card-content"><span class="highlight">当前痛点：</span>日常数据处理任务繁琐、重复。</div></li>
                    <li class="card"><i class="fas fa-exclamation-triangle"></i><div class="card-content"><span class="highlight">潜在风险：</span>耗费大量时间，且人工操作容易出错。</div></li>
                    <li class="card"><i class="fas fa-lightbulb"></i><div class="card-content"><span class="highlight">核心方案：</span>利用AI辅助，自动化完成Python编程。</div></li>
                    <li class="card"><i class="fas fa-rocket"></i><div class="card-content"><span class="highlight">未来机遇：</span>将我们从重复劳动中解放，聚焦更高价值的工作。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 3: 什么是Python -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 1 - 什么是Python？</h2>
                <ul class="card-list">
                    <li class="card">
                        <i class="fas fa-code"></i>
                        <div class="card-content">
                            <span class="highlight">强大工具：</span>
                            </span>一种功能强大的高级编程语言。</span>
                        </div>
                    </li>
                    <li class="card"><i class="fas fa-sitemap"></i><div class="card-content"><span class="highlight">应用广泛：<span class="clickable-text" onclick="showPythonPopup()">在数据分析、办公自动化、AI等领域是绝对主力。</span></span></li>
                    <li class="card"><i class="fas fa-book-open"></i><div class="card-content"><span class="highlight">学习成本：</span>传统方式下存在一定学习门槛，但AI正在改变这一切！</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 4: 痛点聚焦 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 2 - 痛点聚焦：数据处理</h2>
                
                <p class="subtitle" style="margin-bottom: 2rem;">以进口评价推送及抽样数据为例</p>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-spinner fa-spin"></i><div class="card-content"><span class="highlight">繁琐耗时：</span>海量的手动复制、粘贴、核对操作。</div></li>
                    <li class="card"><i class="fas fa-times-circle"></i><div class="card-content"><span class="highlight">容易出错：</span>人工操作难以保证100%的准确性。</div></li>
                    <li class="card"><i class="fas fa-battery-quarter"></i><div class="card-content"><span class="highlight">效率低下：</span>占用了本可用于创造性工作的大量宝贵时间。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 5: 核心解决方案 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 3 - 核心解决方案</h2>
                <p class="subtitle" style="margin-bottom: 2rem;">让AI为我们写代码</p>
                <div class="content-grid">
                    <div class="grid-item">
                        <h3>Before: 手动操作</h3>
                        <p>数小时甚至数天</p>
                        <p>易错、枯燥、效率低</p>
                    </div>
                    <div class="grid-item">
                        <h3>After: AI + Python</h3>
                        <p>几十秒代码运行</p>
                        <p>精准、高效、可复用</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 6: 如何“指挥”AI -->
        <div class="page">
             <div class="page-inner">
                <h2>如何“指挥”AI？三步搞定！</h2>
                <div class="content-grid">
                    <div class="grid-item clickable-card" onclick="showWarningPopup(event)">
                        <i class="fas fa-bullseye"></i>
                        <h3>第一步：明确需求</h3>
                        <p>使用清晰的提示词描述任务及上传参考文件。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-comments"></i>
                        <h3>第二步：沟通迭代</h3>
                        <p>将报错信息反馈给AI，与AI反复沟通，修正Bug。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-shield-alt"></i>
                        <h3>第三步：封装应用</h3>
                        <p>把写好的代码封装成windows可执行应用文件（EXE）。</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 7: 更多可能性 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 4 - AI赋能的更多可能性</h2>
                <div class="content-grid">
                    <div class="grid-item">
                        <i class="fas fa-chart-pie"></i>
                        <h3>数据可视化</h3>
                        <p>让AI自动生成专业的分析图表，让汇报更清晰有力。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-desktop"></i>
                        <h3>创意演示</h3>
                        <p>让AI编写HTML代码，制作比传统PPT更生动的动态演示。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-database"></i>
                        <h3>知识管理</h3>
                        <p>用AI搭建专属知识库，实现法规、资料的秒级快速查询。</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 8: 总结与展望 -->
        <div class="page">
            <div class="page-inner">
                <h2>总结：开启高效工作新模式</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-key"></i><div class="card-content"><span class="highlight">一把“金钥匙”：</span>AI + Python 是解放生产力的强大工具。</div></li>
                    <li class="card"><i class="fas fa-user-astronaut"></i><div class="card-content"><span class="highlight">一个身份转变：</span>从“执行者”转变为智能工具的“指挥者”。</div></li>
                    <li class="card"><i class="fas fa-forward"></i><div class="card-content"><span class="highlight">共同的未来：</span>拥抱新技术，探索AI赋能工作的无限可能。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 9: 结束页 -->
        <div class="page">
            <div class="page-inner">
                <h1>感谢聆听！</h1>
                <p class="subtitle">Q&A</p>
            </div>
        </div>
    </main>

    <!-- Python介绍弹出卡片 -->
    <div class="popup-overlay" id="pythonPopup">
        <div class="popup-card">
            <button class="popup-close" onclick="closePythonPopup()">
                <i class="fas fa-times"></i>
            </button>
            <h2 class="popup-title">
                <i class="fab fa-python"></i> Python 编程语言详解
            </h2>
            <div class="popup-content">
                <div class="popup-section">
                    <h3><i class="fas fa-info-circle"></i>什么是Python？</h3>
                    <p>Python是一种高级、解释型、通用的编程语言，由Guido van Rossum于1991年首次发布。它以简洁、易读的语法著称，被誉为"最接近人类思维"的编程语言。</p>
                </div>

                <div class="popup-section">
                    <h3><i class="fas fa-star"></i>核心特点</h3>
                    <div class="popup-features">
                        <div class="popup-feature">
                            <h4><i class="fas fa-eye"></i>简洁易读</h4>
                            <p>语法简洁明了，代码可读性强，降低程序维护成本</p>
                        </div>
                        <div class="popup-feature">
                            <h4><i class="fas fa-puzzle-piece"></i>跨平台</h4>
                            <p>支持Windows、macOS、Linux等多种操作系统</p>
                        </div>
                        <div class="popup-feature">
                            <h4><i class="fas fa-box-open"></i>丰富生态</h4>
                            <p>拥有超过30万个第三方库，覆盖各个应用领域</p>
                        </div>
                        <div class="popup-feature">
                            <h4><i class="fas fa-users"></i>社区活跃</h4>
                            <p>全球数百万开发者，丰富的学习资源和技术支持</p>
                        </div>
                    </div>
                </div>

                <div class="popup-section">
                    <h3><i class="fas fa-rocket"></i>主要应用领域</h3>
                    <div class="popup-features">
                        <div class="popup-feature">
                            <h4><i class="fas fa-chart-bar"></i>数据科学</h4>
                            <p>NumPy、Pandas、Matplotlib等库让数据分析变得简单</p>
                        </div>
                        <div class="popup-feature">
                            <h4><i class="fas fa-robot"></i>人工智能</h4>
                            <p>TensorFlow、PyTorch等框架的首选语言</p>
                        </div>
                        <div class="popup-feature">
                            <h4><i class="fas fa-globe"></i>Web开发</h4>
                            <p>Django、Flask等框架构建强大的Web应用</p>
                        </div>
                        <div class="popup-feature">
                            <h4><i class="fas fa-cogs"></i>自动化</h4>
                            <p>办公自动化、系统管理、测试自动化的利器</p>
                        </div>
                    </div>
                </div>

                <div class="popup-section">
                    <h3><i class="fas fa-lightbulb"></i>为什么选择Python？</h3>
                    <p><strong>学习曲线平缓：</strong>语法接近自然语言，初学者友好</p>
                    <p><strong>开发效率高：</strong>相比其他语言，Python能用更少的代码实现相同功能</p>
                    <p><strong>就业前景好：</strong>在AI、数据科学、Web开发等热门领域需求旺盛</p>
                    <p><strong>AI时代加持：</strong>结合AI工具，即使是编程新手也能快速上手</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 导航按钮和JS代码保持不变 -->
    <button id="prev-btn" class="nav-button hidden"><i class="fas fa-arrow-left"></i></button>
    <button id="next-btn" class="nav-button"><i class="fas fa-arrow-right"></i></button>
    <ul class="pagination"></ul>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const main = document.querySelector('.main');
            const pages = document.querySelectorAll('.page');
            const paginationContainer = document.querySelector('.pagination');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            const pageCount = pages.length;
            let activeIndex = 0;
            let isAnimating = false;

            paginationContainer.innerHTML = ''; 

            for (let i = 0; i < pageCount; i++) {
                const li = document.createElement('li');
                li.className = 'pagination-item';
                li.dataset.index = i;
                paginationContainer.appendChild(li);
            }
            const paginationItems = document.querySelectorAll('.pagination-item');

            const update = () => {
                isAnimating = true;
                main.style.transform = `translateX(-${activeIndex * 100}vw)`;
                
                pages.forEach((page, index) => {
                    page.classList.toggle('active', index === activeIndex);
                });

                paginationItems.forEach((item, index) => {
                    item.classList.toggle('active', index === activeIndex);
                });

                prevBtn.classList.toggle('hidden', activeIndex === 0);
                nextBtn.classList.toggle('hidden', activeIndex === pageCount - 1);

                setTimeout(() => {
                    isAnimating = false;
                }, 1200);
            };

            const goToPrev = () => {
                if (isAnimating || activeIndex === 0) return;
                activeIndex--;
                update();
            };

            const goToNext = () => {
                if (isAnimating || activeIndex === pageCount - 1) return;
                activeIndex++;
                update();
            };
            
            paginationItems.forEach(item => {
                item.addEventListener('click', () => {
                    if (isAnimating) return;
                    const targetIndex = parseInt(item.dataset.index);
                    if (targetIndex === activeIndex) return;
                    activeIndex = targetIndex;
                    update();
                });
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') goToPrev();
                else if (e.key === 'ArrowRight') goToNext();
            });

            prevBtn.addEventListener('click', goToPrev);
            nextBtn.addEventListener('click', goToNext);

            update();
        });

        // 弹出卡片控制函数
        function showPythonPopup() {
            const popup = document.getElementById('pythonPopup');
            popup.classList.add('active');
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        }

        function closePythonPopup() {
            const popup = document.getElementById('pythonPopup');
            popup.classList.remove('active');
            document.body.style.overflow = 'auto'; // 恢复滚动
        }

        // 点击遮罩层关闭弹窗
        document.getElementById('pythonPopup').addEventListener('click', function(e) {
            if (e.target === this) {
                closePythonPopup();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePythonPopup();
            }
        });
    </script>
</body>
</html>



							<script>
                            	
        document.addEventListener('DOMContentLoaded', () => {
            const main = document.querySelector('.main');
            const pages = document.querySelectorAll('.page');
            const paginationContainer = document.querySelector('.pagination');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            const pageCount = pages.length;
            let activeIndex = 0;
            let isAnimating = false;

            paginationContainer.innerHTML = ''; 

            for (let i = 0; i < pageCount; i++) {
                const li = document.createElement('li');
                li.className = 'pagination-item';
                li.dataset.index = i;
                paginationContainer.appendChild(li);
            }
            const paginationItems = document.querySelectorAll('.pagination-item');

            const update = () => {
                isAnimating = true;
                main.style.transform = `translateX(-${activeIndex * 100}vw)`;
                
                pages.forEach((page, index) => {
                    page.classList.toggle('active', index === activeIndex);
                });

                paginationItems.forEach((item, index) => {
                    item.classList.toggle('active', index === activeIndex);
                });

                prevBtn.classList.toggle('hidden', activeIndex === 0);
                nextBtn.classList.toggle('hidden', activeIndex === pageCount - 1);

                setTimeout(() => {
                    isAnimating = false;
                }, 1200);
            };

            const goToPrev = () => {
                if (isAnimating || activeIndex === 0) return;
                activeIndex--;
                update();
            };

            const goToNext = () => {
                if (isAnimating || activeIndex === pageCount - 1) return;
                activeIndex++;
                update();
            };
            
            paginationItems.forEach(item => {
                item.addEventListener('click', () => {
                    if (isAnimating) return;
                    const targetIndex = parseInt(item.dataset.index);
                    if (targetIndex === activeIndex) return;
                    activeIndex = targetIndex;
                    update();
                });
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') goToPrev();
                else if (e.key === 'ArrowRight') goToNext();
            });

            prevBtn.addEventListener('click', goToPrev);
            nextBtn.addEventListener('click', goToNext);

            update();
        });

        // Python弹出卡片功能
        function showPythonPopup() {
            document.getElementById('pythonPopupOverlay').style.display = 'flex';
        }

        function hidePythonPopup() {
            document.getElementById('pythonPopupOverlay').style.display = 'none';
        }

        // ESC键关闭弹出卡片
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                hidePythonPopup();
                hideWarningPopup();
            }
        });

        // 警告弹出功能
        let warningTimeout;

        function showWarningPopup(event) {
            event.stopPropagation();
            const popup = document.getElementById('warningPopup');
            const card = event.currentTarget;

            // 清除之前的定时器
            if (warningTimeout) {
                clearTimeout(warningTimeout);
            }

            // 计算弹出位置（卡片外面的左下方）
            const cardRect = card.getBoundingClientRect();
            const popupWidth = 200; // 预估弹窗宽度
            const offset = 10; // 与卡片的间距

            // 设置位置：卡片左边缘往左偏移，卡片底部往下偏移
            popup.style.left = (cardRect.left - popupWidth - offset) + 'px';
            popup.style.top = (cardRect.bottom + offset) + 'px';

            // 如果左边空间不够，则显示在右下方
            if (cardRect.left - popupWidth - offset < 0) {
                popup.style.left = (cardRect.right + offset) + 'px';
                popup.style.top = (cardRect.bottom + offset) + 'px';
                // 调整箭头位置
                popup.style.setProperty('--arrow-left', '10px');
            } else {
                popup.style.setProperty('--arrow-left', '30px');
            }

            // 显示弹出框
            popup.classList.add('show');

            // 3秒后自动隐藏
            warningTimeout = setTimeout(() => {
                hideWarningPopup();
            }, 3000);
        }

        function hideWarningPopup() {
            const popup = document.getElementById('warningPopup');
            popup.classList.remove('show');

            if (warningTimeout) {
                clearTimeout(warningTimeout);
                warningTimeout = null;
            }
        }

        // 点击其他地方隐藏警告弹出框
        document.addEventListener('click', function(event) {
            const popup = document.getElementById('warningPopup');
            const card = document.querySelector('.clickable-card');

            if (!card.contains(event.target)) {
                hideWarningPopup();
            }
        });


							</script>

    <!-- Python应用弹出卡片 -->
    <div class="python-popup-overlay" id="pythonPopupOverlay" onclick="hidePythonPopup()">
        <div class="python-popup" onclick="event.stopPropagation()">
            <button class="python-popup-close" onclick="hidePythonPopup()">×</button>
            <h3><i class="fab fa-python"></i>Python 的神奇应用世界</h3>

            <div class="python-apps-grid">
                <div class="python-app-card">
                    <div class="app-icon">📱</div>
                    <h4>移动应用开发</h4>
                    <p>使用Kivy、BeeWare等框架开发跨平台移动应用，一套代码多端运行</p>
                </div>

                <div class="python-app-card">
                    <div class="app-icon">🎮</div>
                    <h4>游戏开发</h4>
                    <p>Pygame让游戏开发变得简单有趣 <span class="pixel-character">🧙‍♂️</span><span class="pixel-character">⚔️</span><span class="pixel-character">🏰</span></p>
                </div>

                <div class="python-app-card">
                    <div class="app-icon">🕷️</div>
                    <h4>网页爬虫</h4>
                    <p>自动化数据采集，从网站获取所需信息，让数据收集变得轻松</p>
                </div>

                <div class="python-app-card">
                    <div class="app-icon">🌐</div>
                    <h4>Web开发</h4>
                    <p>Django、Flask等框架助力快速构建强大的网站和API服务</p>
                </div>

                <div class="python-app-card">
                    <div class="app-icon">🤖</div>
                    <h4>人工智能</h4>
                    <p>机器学习、深度学习的首选语言，TensorFlow、PyTorch等强大工具</p>
                </div>

                <div class="python-app-card">
                    <div class="app-icon">📊</div>
                    <h4>数据分析</h4>
                    <p>pandas、numpy让数据处理变得简单，matplotlib绘制精美图表</p>
                </div>
            </div>
        </div>
    </div>

                        </body>
                        </html>
                    