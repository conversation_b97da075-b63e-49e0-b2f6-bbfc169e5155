
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1920, height=1080, initial-scale=1.0">
    <title>AI+Python提高工作效率 - 动态波浪版</title>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        /* =================================================================== */
        /* === 1. 全局设计变量 === */
        /* =================================================================== */
        :root {
            /* Updated colors for light wave background */
            --text-color: #1d1d1f;
            --heading-color: #1d1d1f;
            --accent-color: #007AFF; /* Apple blue accent */
            --subtle-text-color: rgba(29, 29, 31, 0.8);

            /* Card variables adapted for light background with enhanced glass effect */
            --card-bg-color: rgba(255, 255, 255, 0.25);
            --card-border-color: rgba(255, 255, 255, 0.4);
            --card-blur: 25px;
            --card-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; /* Using Apple's font stack */
            color: var(--text-color);

            /* Clean Apple Keynote-style gradient background */
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            position: relative;
        }

        /* Enhanced dynamic wave animation container */
        .wave-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        /* Dynamic flowing wave layers with enhanced motion */
        .wave {
            position: absolute;
            width: 120%;
            height: 120%;
            animation-iteration-count: infinite;
            animation-timing-function: ease-in-out;
            border-radius: 50%;
        }

        .wave1 {
            background: linear-gradient(45deg,
                rgba(0, 122, 255, 0.05) 0%,
                rgba(0, 122, 255, 0.12) 25%,
                rgba(0, 122, 255, 0.08) 50%,
                rgba(0, 122, 255, 0.04) 75%,
                transparent 100%);
            animation: dynamicWave1 18s ease-in-out infinite;
            transform-origin: 20% 80%;
            left: -10%;
            top: -10%;
        }

        .wave2 {
            background: radial-gradient(ellipse 150% 100% at 70% 30%,
                transparent 0%,
                rgba(88, 86, 214, 0.06) 20%,
                rgba(88, 86, 214, 0.12) 40%,
                rgba(88, 86, 214, 0.08) 60%,
                rgba(88, 86, 214, 0.04) 80%,
                transparent 100%);
            animation: dynamicWave2 22s ease-in-out infinite;
            transform-origin: 70% 30%;
            left: -5%;
            top: -5%;
        }

        .wave3 {
            background: conic-gradient(from 45deg at 40% 60%,
                rgba(175, 82, 222, 0.08) 0deg,
                transparent 60deg,
                rgba(175, 82, 222, 0.12) 120deg,
                transparent 180deg,
                rgba(175, 82, 222, 0.06) 240deg,
                transparent 300deg,
                rgba(175, 82, 222, 0.08) 360deg);
            animation: dynamicWave3 26s ease-in-out infinite;
            transform-origin: 40% 60%;
            left: -8%;
            top: -8%;
        }

        .wave4 {
            background: radial-gradient(ellipse 120% 80% at 80% 20%,
                rgba(255, 45, 146, 0.06) 0%,
                rgba(255, 45, 146, 0.10) 25%,
                rgba(255, 45, 146, 0.08) 50%,
                rgba(255, 45, 146, 0.04) 75%,
                transparent 100%);
            animation: dynamicWave4 20s ease-in-out infinite;
            transform-origin: 80% 20%;
            left: -6%;
            top: -6%;
        }

        .wave5 {
            background: linear-gradient(135deg,
                transparent 0%,
                rgba(52, 199, 89, 0.05) 20%,
                rgba(52, 199, 89, 0.10) 40%,
                rgba(52, 199, 89, 0.08) 60%,
                rgba(52, 199, 89, 0.04) 80%,
                transparent 100%);
            animation: dynamicWave5 24s ease-in-out infinite;
            transform-origin: 60% 40%;
            left: -4%;
            top: -4%;
        }

        .wave6 {
            background: radial-gradient(circle at 90% 90%,
                rgba(255, 159, 10, 0.06) 0%,
                rgba(255, 159, 10, 0.10) 30%,
                rgba(255, 159, 10, 0.06) 60%,
                transparent 100%);
            animation: dynamicWave6 16s ease-in-out infinite;
            transform-origin: 90% 90%;
            left: -7%;
            top: -7%;
        }

        /* Floating particles for enhanced visual effect */
        .particle {
            position: absolute;
            border-radius: 50%;
            pointer-events: none;
            animation: float infinite ease-in-out;
        }

        .particle1 {
            width: 4px;
            height: 4px;
            background: rgba(0, 122, 255, 0.3);
            top: 20%;
            left: 10%;
            animation: float1 8s infinite ease-in-out;
        }

        .particle2 {
            width: 6px;
            height: 6px;
            background: rgba(88, 86, 214, 0.25);
            top: 60%;
            left: 80%;
            animation: float2 12s infinite ease-in-out;
        }

        .particle3 {
            width: 3px;
            height: 3px;
            background: rgba(175, 82, 222, 0.4);
            top: 80%;
            left: 30%;
            animation: float3 10s infinite ease-in-out;
        }

        .particle4 {
            width: 5px;
            height: 5px;
            background: rgba(255, 45, 146, 0.3);
            top: 30%;
            left: 70%;
            animation: float4 14s infinite ease-in-out;
        }

        .particle5 {
            width: 4px;
            height: 4px;
            background: rgba(52, 199, 89, 0.35);
            top: 70%;
            left: 15%;
            animation: float5 9s infinite ease-in-out;
        }

        /* Additional wave layers for more dynamic effect */
        .wave7 {
            background: linear-gradient(60deg,
                rgba(255, 204, 0, 0.04) 0%,
                rgba(255, 204, 0, 0.08) 30%,
                rgba(255, 204, 0, 0.06) 60%,
                transparent 100%);
            animation: dynamicWave7 19s ease-in-out infinite;
            transform-origin: 25% 75%;
            left: -3%;
            top: -3%;
        }

        .wave8 {
            background: radial-gradient(ellipse 200% 150% at 10% 50%,
                rgba(255, 59, 48, 0.05) 0%,
                rgba(255, 59, 48, 0.09) 40%,
                rgba(255, 59, 48, 0.04) 80%,
                transparent 100%);
            animation: dynamicWave8 23s ease-in-out infinite;
            transform-origin: 10% 50%;
            left: -9%;
            top: -9%;
        }

        /* Enhanced dynamic wave animations with fluid motion */
        @keyframes dynamicWave1 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.7;
            }
            25% {
                transform: rotate(90deg) scale(1.15) translate(30px, -20px);
                opacity: 0.9;
            }
            50% {
                transform: rotate(180deg) scale(0.9) translate(-15px, 25px);
                opacity: 0.8;
            }
            75% {
                transform: rotate(270deg) scale(1.08) translate(20px, -10px);
                opacity: 0.95;
            }
            100% {
                transform: rotate(360deg) scale(1) translate(0, 0);
                opacity: 0.7;
            }
        }

        @keyframes dynamicWave2 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.6;
            }
            30% {
                transform: rotate(-120deg) scale(1.2) translate(-25px, 15px);
                opacity: 0.85;
            }
            60% {
                transform: rotate(-240deg) scale(0.85) translate(35px, -30px);
                opacity: 0.75;
            }
            100% {
                transform: rotate(-360deg) scale(1) translate(0, 0);
                opacity: 0.6;
            }
        }

        @keyframes dynamicWave3 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.5;
            }
            20% {
                transform: rotate(72deg) scale(1.25) translate(40px, 20px);
                opacity: 0.8;
            }
            40% {
                transform: rotate(144deg) scale(0.8) translate(-30px, 35px);
                opacity: 0.9;
            }
            60% {
                transform: rotate(216deg) scale(1.1) translate(25px, -25px);
                opacity: 0.7;
            }
            80% {
                transform: rotate(288deg) scale(0.95) translate(-20px, 15px);
                opacity: 0.85;
            }
            100% {
                transform: rotate(360deg) scale(1) translate(0, 0);
                opacity: 0.5;
            }
        }

        @keyframes dynamicWave4 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.8;
            }
            33% {
                transform: rotate(120deg) scale(1.18) translate(-40px, 30px);
                opacity: 0.95;
            }
            66% {
                transform: rotate(240deg) scale(0.88) translate(35px, -20px);
                opacity: 0.75;
            }
            100% {
                transform: rotate(360deg) scale(1) translate(0, 0);
                opacity: 0.8;
            }
        }

        @keyframes dynamicWave5 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.65;
            }
            40% {
                transform: rotate(-144deg) scale(1.12) translate(50px, -35px);
                opacity: 0.9;
            }
            80% {
                transform: rotate(-288deg) scale(0.92) translate(-25px, 40px);
                opacity: 0.8;
            }
            100% {
                transform: rotate(-360deg) scale(1) translate(0, 0);
                opacity: 0.65;
            }
        }

        @keyframes dynamicWave6 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.7;
            }
            50% {
                transform: rotate(180deg) scale(1.3) translate(-45px, 25px);
                opacity: 0.95;
            }
            100% {
                transform: rotate(360deg) scale(1) translate(0, 0);
                opacity: 0.7;
            }
        }

        /* Floating particle animations */
        @keyframes float1 {
            0%, 100% {
                transform: translateY(0px) translateX(0px);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-20px) translateX(15px);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-40px) translateX(-10px);
                opacity: 0.8;
            }
            75% {
                transform: translateY(-20px) translateX(20px);
                opacity: 0.5;
            }
        }

        @keyframes float2 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.25;
            }
            33% {
                transform: translateY(-30px) translateX(-25px) scale(1.2);
                opacity: 0.5;
            }
            66% {
                transform: translateY(-15px) translateX(30px) scale(0.8);
                opacity: 0.7;
            }
        }

        @keyframes float3 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg);
                opacity: 0.4;
            }
            50% {
                transform: translateY(-35px) translateX(25px) rotate(180deg);
                opacity: 0.8;
            }
        }

        @keyframes float4 {
            0%, 100% {
                transform: translateY(0px) translateX(0px);
                opacity: 0.3;
            }
            20% {
                transform: translateY(-15px) translateX(-20px);
                opacity: 0.6;
            }
            40% {
                transform: translateY(-30px) translateX(10px);
                opacity: 0.8;
            }
            60% {
                transform: translateY(-25px) translateX(-15px);
                opacity: 0.7;
            }
            80% {
                transform: translateY(-10px) translateX(25px);
                opacity: 0.5;
            }
        }

        @keyframes float5 {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.35;
            }
            40% {
                transform: translateY(-25px) translateX(20px) scale(1.3);
                opacity: 0.7;
            }
            80% {
                transform: translateY(-45px) translateX(-15px) scale(0.9);
                opacity: 0.6;
            }
        }

        @keyframes dynamicWave7 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.6;
            }
            35% {
                transform: rotate(126deg) scale(1.15) translate(35px, -40px);
                opacity: 0.85;
            }
            70% {
                transform: rotate(252deg) scale(0.9) translate(-30px, 30px);
                opacity: 0.75;
            }
            100% {
                transform: rotate(360deg) scale(1) translate(0, 0);
                opacity: 0.6;
            }
        }

        @keyframes dynamicWave8 {
            0% {
                transform: rotate(0deg) scale(1) translate(0, 0);
                opacity: 0.55;
            }
            25% {
                transform: rotate(-90deg) scale(1.25) translate(-50px, 20px);
                opacity: 0.8;
            }
            50% {
                transform: rotate(-180deg) scale(0.85) translate(40px, -35px);
                opacity: 0.9;
            }
            75% {
                transform: rotate(-270deg) scale(1.1) translate(-25px, 45px);
                opacity: 0.7;
            }
            100% {
                transform: rotate(-360deg) scale(1) translate(0, 0);
                opacity: 0.55;
            }
        }
        
        /* Main layout and transitions remain the same */
        .main {
            width: 100%;
            height: 100%;
            display: flex;
            transition: transform 1s cubic-bezier(0.86, 0, 0.07, 1);
        }

        .page {
            width: 100vw;
            height: 100vh;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 5vw;
        }
        
        .page-inner {
            width: 100%;
            max-width: 1200px;
            text-align: center;
            opacity: 0;
            transform: translateY(30px);
            transition: none;
        }
        .page.active .page-inner {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.8s ease-out 0.4s, transform 0.8s ease-out 0.4s;
        }
        
        /* =================================================================== */
        /* === 2. Card Design (已根据参考文件更新) === */
        /* =================================================================== */

        /* Enhanced glass morphism card style */
        .card, .grid-item {
            background: var(--card-bg-color);
            backdrop-filter: blur(var(--card-blur)) saturate(180%);
            -webkit-backdrop-filter: blur(var(--card-blur)) saturate(180%);
            border-radius: var(--card-radius);
            border: 1px solid var(--card-border-color);
            box-shadow:
                0 8px 32px 0 rgba(0, 0, 0, 0.1),
                0 2px 16px 0 rgba(0, 0, 0, 0.08),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.4);
            transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
        }
        .card { display: flex; align-items: center; text-align: left; padding: 1.5rem 2rem; }
        
        /* Enhanced hover effect for glass morphism */
        .card:hover, .grid-item:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.35);
            box-shadow:
                0 16px 48px 0 rgba(0, 0, 0, 0.15),
                0 4px 24px 0 rgba(0, 0, 0, 0.12),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.6);
        }

        /* All other styles remain largely the same, but adapted for the new theme */
        .content-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; width: 100%; }
        .grid-item { padding: 2rem; }
        
        h1 { font-size: 4rem; font-weight: 600; color: var(--heading-color); margin-bottom: 1rem; }
        h2 { font-size: 3rem; font-weight: 500; color: var(--heading-color); margin-bottom: 3rem; display: inline-block; border-bottom: 2px solid var(--accent-color); padding-bottom: 1rem; }
        h3 { font-size: 1.6rem; font-weight: 500; color: var(--heading-color); margin-bottom: 1rem; }
        p { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .subtitle { font-size: 1.6rem; color: var(--subtle-text-color); font-weight: 300; }
        
        .card-list { list-style: none; display: flex; flex-direction: column; gap: 1.5rem; width: 100%; max-width: 900px; margin: 0 auto; }
        .card-content { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .card i.fas { color: var(--accent-color); font-size: 1.8rem; margin-right: 1.5rem; width: 35px; text-align: center; }
        .highlight { color: var(--accent-color); font-weight: 500; }
        
        .grid-item i.fas { font-size: 2.5rem; margin: 0 0 1rem 0; display: block; width: auto; color: var(--accent-color); }
        
        .warning-box { background-color: var(--card-bg-color); color: var(--text-color); backdrop-filter: blur(var(--card-blur)); border: 1px solid var(--accent-color); padding: 0.8rem 1.5rem; border-radius: 8px; font-weight: 700; display: inline-flex; align-items: center; margin-top: 1rem; }
        .warning-box i { margin-right: 10px; color: var(--accent-color); }

        /* Navigation elements remain the same */
        .pagination { position: fixed; bottom: 30px; left: 50%; transform: translateX(-50%); list-style: none; z-index: 1000; display: flex; gap: 15px; padding: 8px 15px; background: rgba(0, 0, 0, 0.2); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 20px; }
        .pagination-item { width: 12px; height: 12px; border-radius: 50%; background-color: rgba(255, 255, 255, 0.4); cursor: pointer; transition: background-color 0.3s, transform 0.3s; }
        .pagination-item.active { background-color: var(--accent-color); transform: scale(1.4); }
        .nav-button { position: fixed; top: 50%; transform: translateY(-50%); background-color: rgba(255, 255, 255, 0.1); color: white; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 50%; width: 50px; height: 50px; font-size: 20px; cursor: pointer; z-index: 100; display: flex; justify-content: center; align-items: center; transition: background-color 0.3s, opacity 0.3s; opacity: 0.7; backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); }
        .nav-button:hover { background-color: rgba(255, 255, 255, 0.2); opacity: 1; }
        .nav-button.hidden { opacity: 0; pointer-events: none; }
        #prev-btn { left: 30px; }
        #next-btn { right: 30px; }

        /* Main layout and transitions */
        .main {
            width: 100%;
            height: 100%;
            display: flex;
            transition: transform 1s cubic-bezier(0.86, 0, 0.07, 1);
        }

        .page {
            width: 100vw;
            height: 100vh;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 5vw;
        }

        .page-inner {
            width: 100%;
            max-width: 1200px;
            text-align: center;
            opacity: 0;
            transform: translateY(30px);
            transition: none;
        }
        .page.active .page-inner {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.8s ease-out 0.4s, transform 0.8s ease-out 0.4s;
        }

        /* Enhanced glass morphism card style */
        .card, .grid-item {
            background: var(--card-bg-color);
            backdrop-filter: blur(var(--card-blur)) saturate(180%);
            -webkit-backdrop-filter: blur(var(--card-blur)) saturate(180%);
            border-radius: var(--card-radius);
            border: 1px solid var(--card-border-color);
            box-shadow:
                0 8px 32px 0 rgba(0, 0, 0, 0.1),
                0 2px 16px 0 rgba(0, 0, 0, 0.08),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.4);
            transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
        }
        .card { display: flex; align-items: center; text-align: left; padding: 1.5rem 2rem; }

        /* Enhanced hover effect for glass morphism */
        .card:hover, .grid-item:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.35);
            box-shadow:
                0 16px 48px 0 rgba(0, 0, 0, 0.15),
                0 4px 24px 0 rgba(0, 0, 0, 0.12),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.6);
        }

        .content-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; width: 100%; }
        .grid-item { padding: 2rem; }

        h1 { font-size: 4rem; font-weight: 600; color: var(--heading-color); margin-bottom: 1rem; }
        h2 { font-size: 3rem; font-weight: 500; color: var(--heading-color); margin-bottom: 3rem; display: inline-block; border-bottom: 2px solid var(--accent-color); padding-bottom: 1rem; }
        h3 { font-size: 1.6rem; font-weight: 500; color: var(--heading-color); margin-bottom: 1rem; }
        p { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .subtitle { font-size: 1.6rem; color: var(--subtle-text-color); font-weight: 300; }

        .card-list { list-style: none; display: flex; flex-direction: column; gap: 1.5rem; width: 100%; max-width: 900px; margin: 0 auto; }
        .card-content { font-size: 1.4rem; line-height: 1.7; color: var(--subtle-text-color); }
        .card i.fas { color: var(--accent-color); font-size: 1.8rem; margin-right: 1.5rem; width: 35px; text-align: center; }
        .highlight { color: var(--accent-color); font-weight: 500; }

        .grid-item i.fas { font-size: 2.5rem; margin: 0 0 1rem 0; display: block; width: auto; color: var(--accent-color); }

        .warning-box { background-color: var(--card-bg-color); color: var(--text-color); backdrop-filter: blur(var(--card-blur)); border: 1px solid var(--accent-color); padding: 0.8rem 1.5rem; border-radius: 8px; font-weight: 700; display: inline-flex; align-items: center; margin-top: 1rem; }
        .warning-box i { margin-right: 10px; color: var(--accent-color); }
    </style>
</head>
<body>
    <!-- Enhanced dynamic wave background container -->
    <div class="wave-container">
        <div class="wave wave1"></div>
        <div class="wave wave2"></div>
        <div class="wave wave3"></div>
        <div class="wave wave4"></div>
        <div class="wave wave5"></div>
        <div class="wave wave6"></div>

        <!-- SVG Wave layers for realistic wave motion -->
        <div class="svg-wave">
            <svg viewBox="0 0 1920 1080" preserveAspectRatio="none">
                <path class="wave-path1"
                      d="M0,50 Q250,20 500,50 T1000,50 T1500,50 T1920,50 V1080 H0 Z"
                      fill="rgba(0, 122, 255, 0.08)" />
            </svg>
        </div>

        <div class="svg-wave">
            <svg viewBox="0 0 1920 1080" preserveAspectRatio="none">
                <path class="wave-path2"
                      d="M0,60 Q300,30 600,60 T1200,60 T1920,60 V1080 H0 Z"
                      fill="rgba(88, 86, 214, 0.06)" />
            </svg>
        </div>

        <div class="svg-wave">
            <svg viewBox="0 0 1920 1080" preserveAspectRatio="none">
                <path class="wave-path3"
                      d="M0,65 Q400,35 800,65 T1920,65 V1080 H0 Z"
                      fill="rgba(175, 82, 222, 0.05)" />
            </svg>
        </div>

        <!-- Floating particles for enhanced visual effect -->
        <div class="particle particle1"></div>
        <div class="particle particle2"></div>
        <div class="particle particle3"></div>
        <div class="particle particle4"></div>
        <div class="particle particle5"></div>
    </div>

    <main class="main">
        <!-- The HTML content of all 9 pages remains exactly the same -->
        <!-- Slide 1: 封面页 -->
        <div class="page">
            <div class="page-inner">
                <h1>利用AI+Python实现工作效能新飞跃</h1>
                <p class="subtitle">拥抱变革</p>
                <p class="subtitle">演讲人：林东</p>
            </div>
        </div>
        <!-- Slide 2: 挑战与机遇 -->
        <div class="page">
            <div class="page-inner">
                <h2>挑战与机遇</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-cogs"></i><div class="card-content"><span class="highlight">当前痛点：</span>日常数据处理任务繁琐、重复。</div></li>
                    <li class="card"><i class="fas fa-exclamation-triangle"></i><div class="card-content"><span class="highlight">潜在风险：</span>耗费大量时间，且人工操作容易出错。</div></li>
                    <li class="card"><i class="fas fa-lightbulb"></i><div class="card-content"><span class="highlight">核心方案：</span>利用AI辅助，自动化完成Python编程。</div></li>
                    <li class="card"><i class="fas fa-rocket"></i><div class="card-content"><span class="highlight">未来机遇：</span>将我们从重复劳动中解放，聚焦更高价值的工作。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 3: 什么是Python -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 1 - 什么是Python？</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-code"></i><div class="card-content"><span class="highlight">强大工具：</span>一种功能强大的高级编程语言。</div></li>
                    <li class="card"><i class="fas fa-sitemap"></i><div class="card-content"><span class="highlight">应用广泛：</span>在数据分析、办公自动化、AI等领域是绝对主力。</div></li>
                    <li class="card"><i class="fas fa-book-open"></i><div class="card-content"><span class="highlight">学习成本：</span>传统方式下存在一定学习门槛，但AI正在改变这一切！</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 4: 痛点聚焦 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 2 - 痛点聚焦：数据处理</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-spinner fa-spin"></i><div class="card-content"><span class="highlight">繁琐耗时：</span>海量的手动复制、粘贴、核对操作。</div></li>
                    <li class="card"><i class="fas fa-times-circle"></i><div class="card-content"><span class="highlight">容易出错：</span>人工操作难以保证100%的准确性。</div></li>
                    <li class="card"><i class="fas fa-battery-quarter"></i><div class="card-content"><span class="highlight">效率低下：</span>占用了本可用于创造性工作的大量宝贵时间。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 5: 核心解决方案 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 3 - 核心解决方案</h2>
                <p class="subtitle" style="margin-bottom: 2rem;">让AI为我们写代码</p>
                <div class="content-grid">
                    <div class="grid-item">
                        <h3>Before: 手动操作</h3>
                        <p>数小时甚至数天</p>
                        <p>易错、枯燥、效率低</p>
                    </div>
                    <div class="grid-item">
                        <h3>After: AI + Python</h3>
                        <p>几十秒代码运行</p>
                        <p>精准、高效、可复用</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 6: 如何“指挥”AI -->
        <div class="page">
             <div class="page-inner">
                <h2>如何“指挥”AI？三步搞定！</h2>
                <div class="content-grid">
                    <div class="grid-item">
                        <i class="fas fa-bullseye"></i>
                        <h3>第一步：明确需求</h3>
                        <p>使用清晰的提示词描述任务，必要时上传脱敏后的文件。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-comments"></i>
                        <h3>第二步：沟通迭代</h3>
                        <p>将报错信息反馈给AI，与AI反复沟通，修正Bug。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-shield-alt"></i>
                        <h3>第三步：安全第一</h3>
                        <p class="warning-box"><i class="fas fa-exclamation-circle"></i>敏感数据请务必脱敏</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 7: 更多可能性 -->
        <div class="page">
            <div class="page-inner">
                <h2>Part 4 - AI赋能的更多可能性</h2>
                <div class="content-grid">
                    <div class="grid-item">
                        <i class="fas fa-chart-pie"></i>
                        <h3>数据可视化</h3>
                        <p>让AI自动生成专业的分析图表，让汇报更清晰有力。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-desktop"></i>
                        <h3>创意演示</h3>
                        <p>让AI编写HTML代码，制作比传统PPT更生动的动态演示。</p>
                    </div>
                    <div class="grid-item">
                        <i class="fas fa-database"></i>
                        <h3>知识管理</h3>
                        <p>用AI搭建专属知识库，实现法规、资料的秒级快速查询。</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Slide 8: 总结与展望 -->
        <div class="page">
            <div class="page-inner">
                <h2>总结：开启高效工作新模式</h2>
                <ul class="card-list">
                    <li class="card"><i class="fas fa-key"></i><div class="card-content"><span class="highlight">一把“金钥匙”：</span>AI + Python 是解放生产力的强大工具。</div></li>
                    <li class="card"><i class="fas fa-user-astronaut"></i><div class="card-content"><span class="highlight">一个身份转变：</span>从“执行者”转变为智能工具的“指挥者”。</div></li>
                    <li class="card"><i class="fas fa-forward"></i><div class="card-content"><span class="highlight">共同的未来：</span>拥抱新技术，探索AI赋能工作的无限可能。</div></li>
                </ul>
            </div>
        </div>
        <!-- Slide 9: 结束页 -->
        <div class="page">
            <div class="page-inner">
                <h1>感谢聆听！</h1>
                <p class="subtitle">Q&A</p>
            </div>
        </div>
    </main>

    <!-- 导航按钮和JS代码保持不变 -->
    <button id="prev-btn" class="nav-button hidden"><i class="fas fa-arrow-left"></i></button>
    <button id="next-btn" class="nav-button"><i class="fas fa-arrow-right"></i></button>
    <ul class="pagination"></ul>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const main = document.querySelector('.main');
            const pages = document.querySelectorAll('.page');
            const paginationContainer = document.querySelector('.pagination');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            const pageCount = pages.length;
            let activeIndex = 0;
            let isAnimating = false;

            paginationContainer.innerHTML = ''; 

            for (let i = 0; i < pageCount; i++) {
                const li = document.createElement('li');
                li.className = 'pagination-item';
                li.dataset.index = i;
                paginationContainer.appendChild(li);
            }
            const paginationItems = document.querySelectorAll('.pagination-item');

            const update = () => {
                isAnimating = true;
                main.style.transform = `translateX(-${activeIndex * 100}vw)`;
                
                pages.forEach((page, index) => {
                    page.classList.toggle('active', index === activeIndex);
                });

                paginationItems.forEach((item, index) => {
                    item.classList.toggle('active', index === activeIndex);
                });

                prevBtn.classList.toggle('hidden', activeIndex === 0);
                nextBtn.classList.toggle('hidden', activeIndex === pageCount - 1);

                setTimeout(() => {
                    isAnimating = false;
                }, 1200);
            };

            const goToPrev = () => {
                if (isAnimating || activeIndex === 0) return;
                activeIndex--;
                update();
            };

            const goToNext = () => {
                if (isAnimating || activeIndex === pageCount - 1) return;
                activeIndex++;
                update();
            };
            
            paginationItems.forEach(item => {
                item.addEventListener('click', () => {
                    if (isAnimating) return;
                    const targetIndex = parseInt(item.dataset.index);
                    if (targetIndex === activeIndex) return;
                    activeIndex = targetIndex;
                    update();
                });
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') goToPrev();
                else if (e.key === 'ArrowRight') goToNext();
            });

            prevBtn.addEventListener('click', goToPrev);
            nextBtn.addEventListener('click', goToNext);

            update();
        });
    </script>
</body>
</html>



							<script>
                            	
        document.addEventListener('DOMContentLoaded', () => {
            const main = document.querySelector('.main');
            const pages = document.querySelectorAll('.page');
            const paginationContainer = document.querySelector('.pagination');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            const pageCount = pages.length;
            let activeIndex = 0;
            let isAnimating = false;

            paginationContainer.innerHTML = ''; 

            for (let i = 0; i < pageCount; i++) {
                const li = document.createElement('li');
                li.className = 'pagination-item';
                li.dataset.index = i;
                paginationContainer.appendChild(li);
            }
            const paginationItems = document.querySelectorAll('.pagination-item');

            const update = () => {
                isAnimating = true;
                main.style.transform = `translateX(-${activeIndex * 100}vw)`;
                
                pages.forEach((page, index) => {
                    page.classList.toggle('active', index === activeIndex);
                });

                paginationItems.forEach((item, index) => {
                    item.classList.toggle('active', index === activeIndex);
                });

                prevBtn.classList.toggle('hidden', activeIndex === 0);
                nextBtn.classList.toggle('hidden', activeIndex === pageCount - 1);

                setTimeout(() => {
                    isAnimating = false;
                }, 1200);
            };

            const goToPrev = () => {
                if (isAnimating || activeIndex === 0) return;
                activeIndex--;
                update();
            };

            const goToNext = () => {
                if (isAnimating || activeIndex === pageCount - 1) return;
                activeIndex++;
                update();
            };
            
            paginationItems.forEach(item => {
                item.addEventListener('click', () => {
                    if (isAnimating) return;
                    const targetIndex = parseInt(item.dataset.index);
                    if (targetIndex === activeIndex) return;
                    activeIndex = targetIndex;
                    update();
                });
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowLeft') goToPrev();
                else if (e.key === 'ArrowRight') goToNext();
            });

            prevBtn.addEventListener('click', goToPrev);
            nextBtn.addEventListener('click', goToNext);

            update();
        });
    

							</script>
                        </body>
                        </html>
                    