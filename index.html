<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拥抱变革：利用AI+Python实现工作效能新飞跃</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="presentation-container">
        <!-- Navigation -->
        <nav class="nav-bar">
            <div class="nav-dots">
                <span class="dot active" data-slide="0"></span>
                <span class="dot" data-slide="1"></span>
                <span class="dot" data-slide="2"></span>
                <span class="dot" data-slide="3"></span>
                <span class="dot" data-slide="4"></span>
                <span class="dot" data-slide="5"></span>
                <span class="dot" data-slide="6"></span>
                <span class="dot" data-slide="7"></span>
                <span class="dot" data-slide="8"></span>
            </div>
            <div class="nav-controls">
                <button class="nav-btn" id="prevBtn"><i class="fas fa-chevron-left"></i></button>
                <button class="nav-btn" id="nextBtn"><i class="fas fa-chevron-right"></i></button>
            </div>
        </nav>

        <!-- Slides Container -->
        <div class="slides-wrapper">
            <!-- Slide 1: 封面页 -->
            <div class="slide active" data-slide="0">
                <div class="slide-content">
                    <div class="hero-section">
                        <div class="icon-container">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h1 class="main-title">拥抱变革</h1>
                        <h2 class="sub-title">利用AI+Python实现工作效能新飞跃</h2>
                        <div class="author-info glass-card">
                            <i class="fas fa-user"></i>
                            <span>[汇报人/部门]</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2: 痛点与机遇 -->
            <div class="slide" data-slide="1">
                <div class="slide-content">
                    <h1 class="slide-title">我们工作中的挑战与机遇</h1>
                    <div class="content-grid">
                        <div class="card-3d glass-card pain-point">
                            <div class="card-header">
                                <i class="fas fa-exclamation-triangle"></i>
                                <h3>当前痛点</h3>
                            </div>
                            <ul>
                                <li><i class="fas fa-circle"></i> 日常数据处理任务繁琐、重复</li>
                                <li><i class="fas fa-circle"></i> 耗费大量时间，且容易出错</li>
                            </ul>
                        </div>
                        <div class="card-3d glass-card solution">
                            <div class="card-header">
                                <i class="fas fa-lightbulb"></i>
                                <h3>解决方案</h3>
                            </div>
                            <ul>
                                <li><i class="fas fa-check"></i> 利用AI辅助，自动化完成Python编程</li>
                            </ul>
                        </div>
                        <div class="card-3d glass-card opportunity">
                            <div class="card-header">
                                <i class="fas fa-star"></i>
                                <h3>未来机遇</h3>
                            </div>
                            <ul>
                                <li><i class="fas fa-arrow-up"></i> 将我们从重复劳动中解放，聚焦更高价值的工作</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: 认识Python -->
            <div class="slide" data-slide="2">
                <div class="slide-content">
                    <h1 class="slide-title">Part 1 - 什么是Python？</h1>
                    <div class="python-intro">
                        <div class="python-logo">
                            <i class="fab fa-python"></i>
                        </div>
                        <div class="definition glass-card">
                            <h3>一种功能强大的编程语言</h3>
                        </div>
                        <div class="applications-grid">
                            <div class="app-card glass-card">
                                <i class="fas fa-chart-bar"></i>
                                <span>数据分析</span>
                            </div>
                            <div class="app-card glass-card">
                                <i class="fas fa-cogs"></i>
                                <span>办公自动化</span>
                            </div>
                            <div class="app-card glass-card">
                                <i class="fas fa-globe"></i>
                                <span>网站开发</span>
                            </div>
                            <div class="app-card glass-card">
                                <i class="fas fa-brain"></i>
                                <span>人工智能</span>
                            </div>
                        </div>
                        <div class="challenge glass-card warning">
                            <i class="fas fa-info-circle"></i>
                            <p>主要挑战：存在一定的学习成本</p>
                            <p class="highlight">(但AI的出现，正在彻底改变这一点！)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: 工作痛点 -->
            <div class="slide" data-slide="3">
                <div class="slide-content">
                    <h1 class="slide-title">Part 2 - 我们共同的痛点：数据表处理</h1>
                    <div class="pain-points-section">
                        <div class="scenario glass-card">
                            <i class="fas fa-file-excel"></i>
                            <h3>典型场景</h3>
                            <p>进口化学药品评价抽样及通关数据处理</p>
                        </div>
                        <div class="pain-list">
                            <div class="pain-item glass-card">
                                <i class="fas fa-clock"></i>
                                <h4>繁琐耗时</h4>
                                <p>大量的手动复制、粘贴、核对</p>
                            </div>
                            <div class="pain-item glass-card">
                                <i class="fas fa-bug"></i>
                                <h4>容易出错</h4>
                                <p>人工操作难以保证100%的准确性</p>
                            </div>
                            <div class="pain-item glass-card">
                                <i class="fas fa-chart-line-down"></i>
                                <h4>效率低下</h4>
                                <p>占用了本可用于创造性工作的大量时间</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: 核心解决方案 -->
            <div class="slide" data-slide="4">
                <div class="slide-content">
                    <h1 class="slide-title">Part 3 - 核心解决方案：让AI为我们写代码</h1>
                    <div class="solution-section">
                        <div class="core-concept glass-card">
                            <i class="fas fa-magic"></i>
                            <h3>核心理念</h3>
                            <p>我们无需成为编程专家，只需学会<strong>"指挥"</strong>AI</p>
                        </div>
                        <div class="before-after">
                            <div class="before glass-card">
                                <i class="fas fa-hourglass-half"></i>
                                <h4>Before</h4>
                                <p>数小时的手动操作</p>
                            </div>
                            <div class="arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                            <div class="after glass-card">
                                <i class="fas fa-bolt"></i>
                                <h4>After</h4>
                                <p>几十秒的代码运行</p>
                            </div>
                        </div>
                        <div class="effect glass-card highlight">
                            <i class="fas fa-trophy"></i>
                            <h3>效果：精准、高效、可复用</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
