/* 基础样式和重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow: hidden;
    color: #333;
}

/* 毛玻璃效果基础类 */
.glass-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.2);
}

/* 演示容器 */
.presentation-container {
    width: 1920px;
    height: 1080px;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    transform-origin: center;
    transform: scale(calc(100vw / 1920));
}

@media (max-height: 1080px) {
    .presentation-container {
        transform: scale(calc(100vh / 1080));
    }
}

/* 导航栏 */
.nav-bar {
    position: fixed;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 15px 30px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 50px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-dots {
    display: flex;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #fff;
    transform: scale(1.2);
}

.nav-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 幻灯片容器 */
.slides-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
}

.slide {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    padding: 120px 80px 80px;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

.slide.prev {
    transform: translateX(-100%);
}

/* 幻灯片内容 */
.slide-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 标题样式 */
.main-title {
    font-size: 4.5rem;
    font-weight: 700;
    color: white;
    text-align: center;
    margin-bottom: 20px;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
}

.sub-title {
    font-size: 2.5rem;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    margin-bottom: 40px;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.slide-title {
    font-size: 3.5rem;
    font-weight: 600;
    color: white;
    text-align: center;
    margin-bottom: 60px;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 图标容器 */
.icon-container {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40px;
    animation: float 3s ease-in-out infinite;
}

.icon-container i {
    font-size: 4rem;
    color: white;
}

/* 3D卡片效果 */
.card-3d {
    perspective: 1000px;
    transform-style: preserve-3d;
    transition: transform 0.6s;
    padding: 30px;
    margin: 20px;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.card-3d:hover {
    transform: rotateY(10deg) rotateX(5deg) translateZ(20px);
}

/* 内容网格 */
.content-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    width: 100%;
    max-width: 1400px;
}

.card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.card-header i {
    font-size: 2rem;
    color: #fff;
}

.card-header h3 {
    font-size: 1.8rem;
    color: white;
    font-weight: 600;
}

/* 列表样式 */
ul {
    list-style: none;
    padding: 0;
}

li {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
}

li i {
    color: #4CAF50;
}

/* 特殊卡片样式 */
.pain-point {
    border-left: 4px solid #ff6b6b;
}

.solution {
    border-left: 4px solid #4ecdc4;
}

.opportunity {
    border-left: 4px solid #45b7d1;
}

.warning {
    border-left: 4px solid #ffa726;
}

.highlight {
    background: rgba(255, 255, 255, 0.25);
    color: #fff !important;
    font-weight: 600;
}

/* 作者信息 */
.author-info {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px 40px;
    font-size: 1.5rem;
    color: white;
    animation: fadeInUp 1s ease-out 0.4s both;
}

/* 动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Python介绍页面样式 */
.python-intro {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
    width: 100%;
    max-width: 1200px;
}

.python-logo {
    font-size: 8rem;
    color: #3776ab;
    animation: float 3s ease-in-out infinite;
}

.definition {
    padding: 30px 60px;
    text-align: center;
}

.definition h3 {
    font-size: 2.5rem;
    color: white;
    font-weight: 500;
}

.applications-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    width: 100%;
}

.app-card {
    padding: 30px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.app-card i {
    font-size: 3rem;
    color: #4CAF50;
}

.app-card span {
    font-size: 1.3rem;
    color: white;
    font-weight: 500;
}

.challenge {
    padding: 30px;
    text-align: center;
    max-width: 800px;
}

.challenge p {
    font-size: 1.4rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 10px;
}

.challenge .highlight {
    font-size: 1.5rem;
    color: #ffa726;
}

/* 痛点页面样式 */
.pain-points-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 50px;
    width: 100%;
    max-width: 1400px;
}

.scenario {
    padding: 40px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    max-width: 600px;
}

.scenario i {
    font-size: 4rem;
    color: #4CAF50;
}

.scenario h3 {
    font-size: 2.2rem;
    color: white;
    font-weight: 600;
}

.scenario p {
    font-size: 1.4rem;
    color: rgba(255, 255, 255, 0.9);
}

.pain-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    width: 100%;
}

.pain-item {
    padding: 40px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    min-height: 250px;
    justify-content: center;
}

.pain-item i {
    font-size: 3.5rem;
    color: #ff6b6b;
}

.pain-item h4 {
    font-size: 1.8rem;
    color: white;
    font-weight: 600;
}

.pain-item p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

/* 解决方案页面样式 */
.solution-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 50px;
    width: 100%;
    max-width: 1200px;
}

.core-concept {
    padding: 40px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    max-width: 800px;
}

.core-concept i {
    font-size: 4rem;
    color: #9c27b0;
}

.core-concept h3 {
    font-size: 2.2rem;
    color: white;
    font-weight: 600;
}

.core-concept p {
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.before-after {
    display: flex;
    align-items: center;
    gap: 40px;
    width: 100%;
    justify-content: center;
}

.before, .after {
    padding: 40px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    min-width: 300px;
}

.before i {
    font-size: 3.5rem;
    color: #ff6b6b;
}

.after i {
    font-size: 3.5rem;
    color: #4CAF50;
}

.before h4, .after h4 {
    font-size: 1.8rem;
    color: white;
    font-weight: 600;
}

.before p, .after p {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
}

.arrow {
    font-size: 3rem;
    color: white;
    animation: pulse 2s infinite;
}

.effect {
    padding: 30px 60px;
    text-align: center;
}

.effect i {
    font-size: 3rem;
    color: #ffd700;
    margin-right: 15px;
}

.effect h3 {
    font-size: 2rem;
    color: white;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 响应式设计 */
@media (max-width: 1920px) {
    .presentation-container {
        transform: scale(calc(100vw / 1920));
    }
}

@media (max-height: 1080px) {
    .presentation-container {
        transform: scale(calc(100vh / 1080));
    }
}
